{"name": "dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build --docs", "preview-storybook": "serve storybook-static"}, "dependencies": {"@nivo/sankey": "^0.99.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@redux-devtools/extension": "^3.3.0", "@types/chroma-js": "^3.1.1", "@visx/gradient": "^3.12.0", "@visx/sankey": "^3.12.0", "@visx/scale": "^3.12.0", "ag-grid-community": "^33.3.0", "ag-grid-react": "^33.3.0", "browser-dtector": "^4.1.0", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "comlink": "^4.4.2", "d3-array": "^3.2.4", "d3-sankey": "^0.12.3", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "pako": "^2.1.0", "plotly.js": "^3.0.1", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-plotly.js": "^2.6.0", "react-vis": "^1.12.1", "reaviz": "^16.0.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.4", "uuid": "^11.1.0", "valtio": "^2.1.5"}, "devDependencies": {"@playwright/test": "^1.52.0", "@react-buddy/ide-toolbox": "^2.4.0", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/pako": "^2.0.3", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/react-plotly.js": "^2.6.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/browser": "^3.1.4", "@vitest/coverage-istanbul": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.3", "serve": "^14.2.4", "storybook": "^8.6.14", "tailwindcss": "^3.4.0", "typescript": "5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.4", "ws": "^8.18.2"}}