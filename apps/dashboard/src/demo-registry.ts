import React from 'react';
import { RibbonGeminiDemo } from './components/RibbonGeminiDemo';
import { RibbonDevstralDemo } from './components/RibbonDevstralDemo';
import { CommonStatusDemo } from './components/CommonStatusDemo';
import { DeRoundTableDemo } from './components/DeRoundTableDemo';
import { AuctioneerPage } from './components/AuctioneerPage';
import { UserManagementPage } from './components/UserManagementPage';

/**
 * Demo Registry - Central place to manage all demo pages
 *
 * To add a new demo:
 * 1. Import your demo component
 * 2. Add it to the demoPages array (new demos go first)
 * 3. The navigation will automatically update
 */

export type DemoPage = {
  id: string;
  label: string;
  component: React.ComponentType;
  description?: string;
};

export const demoPages: DemoPage[] = [
  {
    id: 'ribbon-gemini',
    label: '3D Ribbon (Gemini)',
    component: RibbonGeminiDemo,
    description: '3D ribbon chart with comprehensive React hooks implementation'
  },
  {
    id: 'ribbon-devstral',
    label: '3D Ribbon (Devstral)',
    component: RibbonDevstralDemo,
    description: '3D ribbon chart with simplified direct DOM implementation'
  },
  {
    id: 'status',
    label: 'Status Widget',
    component: CommonStatusDemo,
    description: 'Auction status widget with animated countdown timer'
  },
  {
    id: 'round-table',
    label: 'Round Table',
    component: DeRoundTableDemo,
    description: 'Interactive round table with real-time updates and scrolling'
  },
  {
    id: 'auctioneer',
    label: 'Auctioneer',
    component: AuctioneerPage,
    description: 'Auctioneer dashboard with various demo components'
  },
  {
    id: 'users',
    label: 'Users',
    component: UserManagementPage,
    description: 'User and company management interface'
  },
];

/**
 * Get demo page by ID
 */
export const getDemoPage = (id: string): DemoPage | undefined => {
  return demoPages.find(page => page.id === id);
};

/**
 * Get the default demo page (first in the list)
 */
export const getDefaultDemoPage = (): DemoPage => {
  return demoPages[0];
};
