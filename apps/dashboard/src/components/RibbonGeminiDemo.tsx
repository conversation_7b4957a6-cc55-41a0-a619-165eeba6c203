import React from 'react';
import RibbonGemini from '../widgets/3d-ribbon/3d-ribbon-gemini';

export const RibbonGeminiDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            3D Ribbon Chart - Gemini Implementation
          </h1>
          <p className="text-gray-400 text-lg">
            Interactive 3D ribbon plot with comprehensive controls and animations. 
            This implementation follows the detailed Gemini requirements with proper React hooks, 
            event handling, and cleanup.
          </p>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-6">
          <RibbonGemini />
        </div>
        
        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Features</h2>
          <ul className="text-gray-300 space-y-2">
            <li>• Interactive 3D surface plots with multiple ribbons</li>
            <li>• Real-time parameter controls (colorscale, opacity, lighting)</li>
            <li>• Rotation limiting with configurable angle constraints</li>
            <li>• Animated data updates with automatic cleanup</li>
            <li>• Proper React hooks implementation with useRef and useCallback</li>
            <li>• Memory leak prevention with proper event listener cleanup</li>
            <li>• Camera reset functionality</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
