import React, { useState, useEffect, useRef, useCallback } from 'react';
import Plot from 'react-plotly.js';

const RibbonGemini = () => {
    const [colorscale, setColorscale] = useState('Viridis');
    const [opacity, setOpacity] = useState(1);
    const [ambient, setAmbient] = useState(0.5);
    const [diffuse, setDiffuse] = useState(0.5);
    const [rotationLimit, setRotationLimit] = useState(360);
    const [plotData, setPlotData] = useState<any[]>([]);
    const [plotLayout, setPlotLayout] = useState<any>({});
    const [isLoading, setIsLoading] = useState(true);

    const figureDataRef = useRef<any>(null);
    const animationIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const animationCountRef = useRef(0);
    const rotatingRef = useRef(false);

    const maxIterations = 10;

    // Initialize plot data
    const initPlot = useCallback(() => {
        if (!figureDataRef.current) return;

        const newPlotData = [];
        const maxPoints = 50;

        for (let i = 0; i < 7; i++) {
            const trace = {
                x: figureDataRef.current[i].x.slice(0, maxPoints),
                y: figureDataRef.current[i].y.slice(0, maxPoints),
                z: figureDataRef.current[i].z.slice(0, maxPoints).map((row: any[]) => row.slice(0, maxPoints)),
                name: '',
                colorscale: colorscale,
                opacity: opacity,
                lighting: {
                    ambient: ambient,
                    diffuse: diffuse,
                    specular: 0.1,
                    roughness: 0.9,
                    fresnel: 0.2,
                },
                type: 'surface',
                showscale: false,
            };
            newPlotData.push(trace);
        }

        const newLayout = {
            title: 'Ribbon Plot (Gemini Implementation)',
            showlegend: false,
            autosize: true,
            scene: {
                xaxis: { title: 'Sample #' },
                yaxis: { title: 'Wavelength' },
                zaxis: { title: 'OD' },
                camera: {
                    eye: { x: 1.5, y: 1.5, z: 1 },
                    up: { x: 0, y: 0, z: 1 },
                },
                dragmode: 'turntable',
            },
        };

        setPlotData(newPlotData);
        setPlotLayout(newLayout);
        setIsLoading(false);
    }, [colorscale, opacity, ambient, diffuse]);

    // Handle rotation limiting via onRelayout callback
    const handleRelayout = useCallback((eventData: any) => {
        if (eventData['scene.camera'] && !rotatingRef.current) {
            rotatingRef.current = true;
            const camera = eventData['scene.camera'];
            const eye = camera.eye;
            const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
            let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

            const limitAngle = rotationLimit / 2;
            if (theta > limitAngle) {
                theta = limitAngle;
            } else if (theta < -limitAngle) {
                theta = -limitAngle;
            }

            const newEyeX = r * Math.cos((theta * Math.PI) / 180);
            const newEyeY = r * Math.sin((theta * Math.PI) / 180);

            const updatedLayout = {
                ...plotLayout,
                scene: {
                    ...plotLayout.scene,
                    camera: {
                        ...plotLayout.scene.camera,
                        eye: { x: newEyeX, y: newEyeY, z: camera.eye.z },
                    },
                },
            };

            setPlotLayout(updatedLayout);
            setTimeout(() => {
                rotatingRef.current = false;
            }, 100);
        }
    }, [rotationLimit, plotLayout]);

    // Update plot when control values change
    useEffect(() => {
        if (plotData.length === 0) return;

        const updatedPlotData = plotData.map(trace => ({
            ...trace,
            colorscale: colorscale,
            opacity: opacity,
            lighting: {
                ...trace.lighting,
                ambient: ambient,
                diffuse: diffuse,
            },
        }));

        setPlotData(updatedPlotData);
    }, [colorscale, opacity, ambient, diffuse]);

    // Reset camera to default position
    const resetCamera = () => {
        const updatedLayout = {
            ...plotLayout,
            scene: {
                ...plotLayout.scene,
                camera: {
                    eye: { x: 1.5, y: 1.5, z: 1 },
                    up: { x: 0, y: 0, z: 1 },
                },
            },
        };
        setPlotLayout(updatedLayout);
    };

    // Start animation
    const startAnimation = useCallback(() => {
        if (animationIntervalRef.current) clearInterval(animationIntervalRef.current);
        animationCountRef.current = 0;

        animationIntervalRef.current = setInterval(() => {
            if (plotData.length === 0) {
                if (animationIntervalRef.current) clearInterval(animationIntervalRef.current);
                return;
            }

            setPlotData(currentData => {
                const newData = currentData.map(trace => ({
                    ...trace,
                    z: trace.z.map((row: number[]) =>
                        row.map((val: number) => val + 0.1)
                    ),
                }));
                return newData;
            });

            animationCountRef.current++;
            if (animationCountRef.current >= maxIterations) {
                if (animationIntervalRef.current) clearInterval(animationIntervalRef.current);
                animationIntervalRef.current = null;
            }
        }, 1000);
    }, [plotData.length, maxIterations]);

    // Fetch data and initialize plot on mount
    useEffect(() => {
        let isMounted = true;

        const fetchDataAndInit = async () => {
            try {
                const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
                const figure = await response.json();
                if (isMounted) {
                    figureDataRef.current = figure.data;
                    initPlot();
                    startAnimation();
                }
            } catch (error) {
                console.error("Failed to fetch or initialize plot:", error);
                setIsLoading(false);
            }
        };

        fetchDataAndInit();

        return () => {
            isMounted = false;
            if (animationIntervalRef.current) {
                clearInterval(animationIntervalRef.current);
            }
        };
    }, [initPlot, startAnimation]);

    return (
        <div className="p-4">
            <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Controls</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label htmlFor="colorscale" className="block text-sm font-medium text-gray-300 mb-2">
                            Colorscale:
                        </label>
                        <select
                            id="colorscale"
                            value={colorscale}
                            onChange={(e) => setColorscale(e.target.value)}
                            className="w-full px-3 py-2 bg-gray-700 text-white rounded-md border border-gray-600 focus:border-blue-500 focus:outline-none"
                        >
                            <option value="Viridis">Viridis</option>
                            <option value="Cividis">Cividis</option>
                            <option value="Hot">Hot</option>
                            <option value="Electric">Electric</option>
                            <option value="Earth">Earth</option>
                        </select>
                    </div>

                    <div>
                        <label htmlFor="opacity" className="block text-sm font-medium text-gray-300 mb-2">
                            Opacity: {opacity.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="opacity"
                            min="0"
                            max="1"
                            step="0.1"
                            value={opacity}
                            onChange={(e) => setOpacity(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="ambient" className="block text-sm font-medium text-gray-300 mb-2">
                            Ambient Lighting: {ambient.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="ambient"
                            min="0"
                            max="1"
                            step="0.1"
                            value={ambient}
                            onChange={(e) => setAmbient(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="diffuse" className="block text-sm font-medium text-gray-300 mb-2">
                            Diffuse Lighting: {diffuse.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="diffuse"
                            min="0"
                            max="1"
                            step="0.1"
                            value={diffuse}
                            onChange={(e) => setDiffuse(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="rotationLimit" className="block text-sm font-medium text-gray-300 mb-2">
                            Rotation Limit: {rotationLimit}°
                        </label>
                        <input
                            type="range"
                            id="rotationLimit"
                            min="0"
                            max="360"
                            step="10"
                            value={rotationLimit}
                            onChange={(e) => setRotationLimit(parseInt(e.target.value, 10))}
                            className="w-full"
                        />
                    </div>

                    <div className="flex items-end">
                        <button
                            onClick={resetCamera}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Reset Camera View
                        </button>
                    </div>
                </div>
            </div>
            {isLoading ? (
                <div className="w-full h-96 bg-white rounded-lg flex items-center justify-center">
                    <div className="text-gray-600">Loading 3D ribbon chart...</div>
                </div>
            ) : (
                <Plot
                    data={plotData}
                    layout={plotLayout}
                    style={{ width: '100%', height: '600px' }}
                    config={{ responsive: true }}
                    onRelayout={handleRelayout}
                />
            )}
        </div>
    );
};

export default RibbonGemini;
