import React, { useState, useEffect } from 'react';
import Plotly from 'plotly.js-dist';

const RibbonDevstral = () => {
  const [colorscale, setColorscale] = useState('Viridis');
  const [opacity, setOpacity] = useState(1);
  const [ambient, setAmbient] = useState(0.5);
  const [diffuse, setDiffuse] = useState(0.5);
  const [rotationLimit, setRotationLimit] = useState(360);

  let data: any[] = [];
  let layout: any = {};
  let animationInterval: NodeJS.Timeout;
  let figureData: any;
  const maxIterations = 10;
  let animationCount = 0;
  let relayoutHandler: any;
  let rotating = false;

  useEffect(() => {
    const fetchData = async () => {
      const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
      const figure = await response.json();
      figureData = figure.data;
      initPlot();
      startAnimation();
    };

    fetchData();

    return () => clearInterval(animationInterval); // Cleanup on component unmount
  }, []);

  const initPlot = () => {
    data = [];

    const maxPoints = 50;

    for (let i = 0; i < 7; i++) {
      const trace = {
        x: figureData[i].x.slice(0, maxPoints),
        y: figureData[i].y.slice(0, maxPoints),
        z: figureData[i].z.slice(0, maxPoints).map((row: any[]) => row.slice(0, maxPoints)),
        name: '',
        colorscale,
        opacity,
        lighting: {
          ambient,
          diffuse,
          specular: 0.1,
          roughness: 0.9,
          fresnel: 0.2,
        },
        type: 'surface',
        showscale: false,
      };
      data.push(trace);
    }

    layout = {
      title: 'Ribbon Plot (Devstral Implementation)',
      showlegend: false,
      autosize: true,
      scene: {
        xaxis: { title: 'Sample #' },
        yaxis: { title: 'Wavelength' },
        zaxis: { title: 'OD' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1 },
          up: { x: 0, y: 0, z: 1 },
        },
        dragmode: 'turntable',
      },
    };

    Plotly.newPlot('myDiv', data, layout);
    setUpRotationLimit();
  };

  const setUpRotationLimit = () => {
    const myDiv = document.getElementById('myDiv');

    if (myDiv) {
      if (relayoutHandler) {
        (myDiv as any).removeEventListener('plotly_relayout', relayoutHandler);
      }

      relayoutHandler = (eventData: any) => {
        if (eventData['scene.camera'] && !rotating) {
          rotating = true;
          const camera = eventData['scene.camera'];
          const eye = camera.eye;
          const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
          let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

          const limitAngle = rotationLimit / 2;
          if (theta > limitAngle) {
            theta = limitAngle;
          } else if (theta < -limitAngle) {
            theta = -limitAngle;
          }

          const newEyeX = r * Math.cos((theta * Math.PI) / 180);
          const newEyeY = r * Math.sin((theta * Math.PI) / 180);

          const update = {
            'scene.camera.eye.x': newEyeX,
            'scene.camera.eye.y': newEyeY,
          };

          Plotly.relayout('myDiv', update).then(() => {
            rotating = false;
          });
        }
      };

      (myDiv as any).addEventListener('plotly_relayout', relayoutHandler);
    }
  };

  const updatePlot = () => {
    data.forEach((trace) => {
      trace.colorscale = colorscale;
      trace.opacity = opacity;
      trace.lighting = {
        ambient,
        diffuse,
        specular: 0.1,
        roughness: 0.9,
        fresnel: 0.2,
      };
    });

    Plotly.react('myDiv', data, layout);
  };

  const resetCamera = () => {
    Plotly.relayout('myDiv', {
      'scene.camera': {
        eye: { x: 1.5, y: 1.5, z: 1 },
        up: { x: 0, y: 0, z: 1 },
      },
    });
  };

  const startAnimation = () => {
    animationInterval = setInterval(() => {
      for (let i = 0; i < data.length; i++) {
        for (let j = 0; j < data[i].z.length; j++) {
          for (let k = 0; k < data[i].z[j].length; k++) {
            data[i].z[j][k] += 0.1;
          }
        }
      }

      Plotly.animate(
        'myDiv',
        {
          data,
        },
        {
          transition: {
            duration: 500,
            easing: 'linear',
          },
          frame: {
            duration: 500,
            redraw: false,
          },
        }
      );

      animationCount++;
      if (animationCount >= maxIterations) {
        clearInterval(animationInterval);
      }
    }, 1000);
  };

  useEffect(() => {
    updatePlot();
  }, [colorscale, opacity, ambient, diffuse]);

  return (
    <div className="p-4">
      <div className="mb-6 bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Controls</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label htmlFor="colorscale" className="block text-sm font-medium text-gray-300 mb-2">
              Colorscale:
            </label>
            <select
              id="colorscale"
              value={colorscale}
              onChange={(e) => setColorscale(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 text-white rounded-md border border-gray-600 focus:border-blue-500 focus:outline-none"
            >
              <option value="Viridis">Viridis</option>
              <option value="Cividis">Cividis</option>
              <option value="Hot">Hot</option>
              <option value="Electric">Electric</option>
              <option value="Earth">Earth</option>
            </select>
          </div>

          <div>
            <label htmlFor="opacity" className="block text-sm font-medium text-gray-300 mb-2">
              Opacity: {opacity}
            </label>
            <input
              type="range"
              id="opacity"
              min="0"
              max="1"
              step="0.1"
              value={opacity}
              onChange={(e) => setOpacity(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <label htmlFor="ambient" className="block text-sm font-medium text-gray-300 mb-2">
              Ambient Lighting: {ambient}
            </label>
            <input
              type="range"
              id="ambient"
              min="0"
              max="1"
              step="0.1"
              value={ambient}
              onChange={(e) => setAmbient(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <label htmlFor="diffuse" className="block text-sm font-medium text-gray-300 mb-2">
              Diffuse Lighting: {diffuse}
            </label>
            <input
              type="range"
              id="diffuse"
              min="0"
              max="1"
              step="0.1"
              value={diffuse}
              onChange={(e) => setDiffuse(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <label htmlFor="rotationLimit" className="block text-sm font-medium text-gray-300 mb-2">
              Rotation Limit: {rotationLimit}°
            </label>
            <input
              type="range"
              id="rotationLimit"
              min="0"
              max="360"
              step="10"
              value={rotationLimit}
              onChange={(e) => setRotationLimit(parseInt(e.target.value))}
              className="w-full"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={resetCamera}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Reset Camera View
            </button>
          </div>
        </div>
      </div>
      <div
        id="myDiv"
        className="w-full bg-white rounded-lg"
        style={{ width: '100%', height: '600px' }}
      />
    </div>
  );
};

export default RibbonDevstral;
