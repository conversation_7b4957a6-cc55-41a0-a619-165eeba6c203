import React, { useState, useEffect } from 'react';
import Plotly from 'plotly.js-dist';

const App = () => {
  const [colorscale, setColorscale] = useState('Viridis');
  const [opacity, setOpacity] = useState(1);
  const [ambient, setAmbient] = useState(0.5);
  const [diffuse, setDiffuse] = useState(0.5);
  const [rotationLimit, setRotationLimit] = useState(360);

  let data = [];
  let layout = {};
  let animationInterval;
  let figureData;
  const maxIterations = 10;
  let animationCount = 0;
  let relayoutHandler;

  useEffect(() => {
    const fetchData = async () => {
      const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
      const figure = await response.json();
      figureData = figure.data;
      initPlot();
      startAnimation();
    };

    fetchData();

    return () => clearInterval(animationInterval); // Cleanup on component unmount
  }, []);

  const initPlot = () => {
    data = [];

    const maxPoints = 50;

    for (let i = 0; i < 7; i++) {
      const trace = {
        x: figureData[i].x.slice(0, maxPoints),
        y: figureData[i].y.slice(0, maxPoints),
        z: figureData[i].z.slice(0, maxPoints).map((row) => row.slice(0, maxPoints)),
        name: '',
        colorscale,
        opacity,
        lighting: {
          ambient,
          diffuse,
          specular: 0.1,
          roughness: 0.9,
          fresnel: 0.2,
        },
        type: 'surface',
        showscale: false,
      };
      data.push(trace);
    }

    layout = {
      title: 'Ribbon Plot',
      showlegend: false,
      autosize: true,
      scene: {
        xaxis: { title: 'Sample #' },
        yaxis: { title: 'Wavelength' },
        zaxis: { title: 'OD' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1 },
          up: { x: 0, y: 0, z: 1 },
        },
        dragmode: 'turntable',
      },
    };

    Plotly.newPlot('myDiv', data, layout);
    setUpRotationLimit();
  };

  const setUpRotationLimit = () => {
    const myDiv = document.getElementById('myDiv');

    if (myDiv) {
      if (relayoutHandler) {
        myDiv.removeEventListener('plotly_relayout', relayoutHandler);
      }

      relayoutHandler = (eventData) => {
        if (eventData['scene.camera'] && !rotating) {
          rotating = true;
          const camera = eventData['scene.camera'];
          const eye = camera.eye;
          const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
          let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

          const limitAngle = rotationLimit / 2;
          if (theta > limitAngle) {
            theta = limitAngle;
          } else if (theta < -limitAngle) {
            theta = -limitAngle;
          }

          const newEyeX = r * Math.cos((theta * Math.PI) / 180);
          const newEyeY = r * Math.sin((theta * Math.PI) / 180);

          const update = {
            'scene.camera.eye.x': newEyeX,
            'scene.camera.eye.y': newEyeY,
          };

          Plotly.relayout('myDiv', update).then(() => {
            rotating = false;
          });
        }
      };

      myDiv.addEventListener('plotly_relayout', relayoutHandler);
    }
  };

  const updatePlot = () => {
    data.forEach((trace) => {
      trace.colorscale = colorscale;
      trace.opacity = opacity;
      trace.lighting = {
        ambient,
        diffuse,
        specular: 0.1,
        roughness: 0.9,
        fresnel: 0.2,
      };
    });

    Plotly.react('myDiv', data, layout);
  };

  const resetCamera = () => {
    Plotly.relayout('myDiv', {
      'scene.camera': {
        eye: { x: 1.5, y: 1.5, z: 1 },
        up: { x: 0, y: 0, z: 1 },
      },
    });
  };

  const startAnimation = () => {
    animationInterval = setInterval(() => {
      for (let i = 0; i < data.length; i++) {
        for (let j = 0; j < data[i].z.length; j++) {
          for (let k = 0; k < data[i].z[j].length; k++) {
            data[i].z[j][k] += 0.1;
          }
        }
      }

      Plotly.animate(
        'myDiv',
        {
          data,
        },
        {
          transition: {
            duration: 500,
            easing: 'linear',
          },
          frame: {
            duration: 500,
            redraw: false,
          },
        }
      );

      animationCount++;
      if (animationCount >= maxIterations) {
        clearInterval(animationInterval);
      }
    }, 1000);
  };

  useEffect(() => {
    updatePlot();
  }, [colorscale, opacity, ambient, diffuse]);

  return (
    <div>
      <div id="controls">
        <h3>Controls</h3>
        <label htmlFor="colorscale">Colorscale:</label>
        <select
          id="colorscale"
          value={colorscale}
          onChange={(e) => setColorscale(e.target.value)}
        >
          <option value="Viridis">Viridis</option>
          <option value="Cividis">Cividis</option>
          <option value="Hot">Hot</option>
          <option value="Electric">Electric</option>
          <option value="Earth">Earth</option>
        </select>
        <br />
        <label htmlFor="opacity">Opacity:</label>
        <input
          type="range"
          id="opacity"
          min="0"
          max="1"
          step="0.1"
          value={opacity}
          onChange={(e) => setOpacity(parseFloat(e.target.value))}
        />
        <span>{opacity}</span>
        <br />
        <label htmlFor="ambient">Ambient Lighting:</label>
        <input
          type="range"
          id="ambient"
          min="0"
          max="1"
          step="0.1"
          value={ambient}
          onChange={(e) => setAmbient(parseFloat(e.target.value))}
        />
        <span>{ambient}</span>
        <br />
        <label htmlFor="diffuse">Diffuse Lighting:</label>
        <input
          type="range"
          id="diffuse"
          min="0"
          max="1"
          step="0.1"
          value={diffuse}
          onChange={(e) => setDiffuse(parseFloat(e.target.value))}
        />
        <span>{diffuse}</span>
        <br />
        <label htmlFor="rotationLimit">Rotation Limit (°):</label>
        <input
          type="range"
          id="rotationLimit"
          min="0"
          max="360"
          step="10"
          value={rotationLimit}
          onChange={(e) => setRotationLimit(parseInt(e.target.value))}
        />
        <span>{rotationLimit}</span>
        <br /><br />
        <button onClick={resetCamera}>Reset Camera View</button>
      </div>
      <div id="myDiv" style={{ width: '100%', height: '600px' }}></div>
    </div>
  );
};

export default App;
